#include <iostream>
#include <fstream>
#include <sstream>
#include <filesystem>
#include <cstdlib>

// 格式化函数：输入文件路径，对文件进行clang格式化
bool format_cpp_file(const std::string& file_path) {
    try {
        // 检查文件是否存在
        if (!std::filesystem::exists(file_path)) {
            std::cerr << "错误：文件不存在: " << file_path << std::endl;
            return false;
        }
        
        // 检查文件是否存在
        std::filesystem::path path(file_path);
        
        // 构建clang-format命令
        std::string command = "clang-format -i \"" + file_path + "\"";
        
        // 执行格式化命令
        int result = system(command.c_str());
        
        if (result == 0) {
            std::cout << "成功格式化文件: " << file_path << std::endl;
            return true;
        } else {
            std::cerr << "格式化失败，返回码: " << result << std::endl;
            return false;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "格式化过程中发生错误: " << e.what() << std::endl;
        return false;
    }
}

int main() {
    // 在这里补全你要格式化的文件路径
    std::string file_path = "D:\\XuSong\\Project\\shaderProfile\\shader\\RGA_Test\\result\\BasicLightingPS.hlsl"; // 请替换为实际的文件路径
    
    std::cout << "开始格式化文件: " << file_path << std::endl;
    
    bool success = format_cpp_file(file_path);
    
    if (success) {
        std::cout << "格式化完成！" << std::endl;
        return 0;
    } else {
        std::cerr << "格式化失败！" << std::endl;
        return 1;
    }
} 